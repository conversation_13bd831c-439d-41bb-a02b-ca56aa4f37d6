import os
import sys
from dotenv import load_dotenv # .env
load_dotenv()

import httpx
from openai import AsyncOpenAI
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai import Agent
from pydantic_ai.providers.openai import OpenAIProvider

def create_provider(api_key: str, base_url: str, proxy_url: str = None) -> OpenAIProvider:
    """创建 OpenAI Provider"""
    if proxy_url:
        http_client = httpx.AsyncClient(proxy=proxy_url, timeout=60.0, verify=True)
        client = AsyncOpenAI(base_url=base_url, api_key=api_key, http_client=http_client)
        return OpenAIProvider(openai_client=client)
    else:
        return OpenAIProvider(base_url=base_url, api_key=api_key)

def run_conversation(agent: Agent) -> None:
    """运行对话测试"""
    questions = [
        "你好，请介绍一下你自己。",
        "请用中文解释什么是人工智能？"
    ]
    for i, question in enumerate(questions, 1):
        print(f"\n🤖 对话 {i}...")
        result = agent.run_sync(question)
        print(f"✅ AI 回复: {result.output}")

def main()->None:
    print("Hello World!")
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL")
    proxy_url = os.getenv("PROXY_URL")#"http://************:8080"
    model_name = "gpt-3.5-turbo"
    model = OpenAIModel(model_name, provider = create_provider(api_key, base_url, proxy_url))
    agent = Agent(model=model,
                system_prompt="You are a helpful assistant that can perform various tasks.")
    run_conversation(agent)

if __name__ == "__main__":
    main()
