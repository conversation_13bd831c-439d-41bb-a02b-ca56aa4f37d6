#!/usr/bin/env python3
"""
基础测试脚本 - 不依赖外部包
测试Python环境和基本功能
"""

import os
import sys

def test_python_environment():
    """测试Python环境"""
    print("🐍 Python 环境测试")
    print("=" * 40)
    print(f"Python 版本: {sys.version}")
    print(f"Python 路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"脚本路径: {__file__}")
    
def test_basic_functionality():
    """测试基本功能"""
    print("\n🔧 基本功能测试")
    print("=" * 40)
    
    # 测试字符串操作
    test_string = "Hello, 世界!"
    print(f"✅ 字符串测试: {test_string}")
    
    # 测试列表操作
    test_list = [1, 2, 3, "测试", "test"]
    print(f"✅ 列表测试: {test_list}")
    
    # 测试字典操作
    test_dict = {"name": "AI助手", "version": "1.0", "language": "Python"}
    print(f"✅ 字典测试: {test_dict}")
    
    # 测试函数
    def greet(name):
        return f"你好, {name}!"
    
    print(f"✅ 函数测试: {greet('用户')}")

def test_file_operations():
    """测试文件操作"""
    print("\n📁 文件操作测试")
    print("=" * 40)
    
    # 检查当前目录文件
    current_files = os.listdir('.')
    print(f"✅ 当前目录文件: {current_files}")
    
    # 测试文件写入和读取
    test_file = "test_output.txt"
    try:
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("这是一个测试文件\nPython 测试成功!")
        
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"✅ 文件读写测试成功")
        print(f"文件内容: {repr(content)}")
        
        # 清理测试文件
        os.remove(test_file)
        print(f"✅ 测试文件已清理")
        
    except Exception as e:
        print(f"❌ 文件操作失败: {e}")

def check_required_modules():
    """检查所需模块的可用性"""
    print("\n📦 模块可用性检查")
    print("=" * 40)
    
    required_modules = [
        'os', 'sys', 'json', 'urllib', 'http'
    ]
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}: 可用")
        except ImportError:
            print(f"❌ {module}: 不可用")

def simulate_ai_conversation():
    """模拟AI对话功能"""
    print("\n🤖 模拟AI对话测试")
    print("=" * 40)
    
    # 模拟对话数据
    conversations = [
        {"user": "你好，请介绍一下你自己。", "ai": "你好！我是一个AI助手，很高兴为您服务。"},
        {"user": "请用中文解释什么是人工智能？", "ai": "人工智能(AI)是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。"}
    ]
    
    for i, conv in enumerate(conversations, 1):
        print(f"\n🤖 对话 {i}...")
        print(f"用户: {conv['user']}")
        print(f"✅ AI 回复: {conv['ai']}")

def main():
    """主函数"""
    print("🚀 Python 基础测试程序")
    print("=" * 50)
    
    try:
        test_python_environment()
        test_basic_functionality()
        test_file_operations()
        check_required_modules()
        simulate_ai_conversation()
        
        print("\n🎉 所有基础测试完成！")
        print("Python 环境运行正常，可以继续安装外部依赖包。")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
