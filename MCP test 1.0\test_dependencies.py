#!/usr/bin/env python3
"""
依赖检查脚本
检查所有必需的包是否正确安装
"""

import sys

def check_package(package_name, import_name=None):
    """检查单个包是否可以导入"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        print(f"✅ {package_name}: 已安装")
        return True
    except ImportError as e:
        print(f"❌ {package_name}: 未安装 - {e}")
        return False

def main():
    print("🔍 检查依赖包安装状态...")
    print("=" * 40)
    
    packages = [
        ("python-dotenv", "dotenv"),
        ("httpx", "httpx"),
        ("openai", "openai"),
        ("pydantic-ai", "pydantic_ai"),
    ]
    
    all_installed = True
    for package_name, import_name in packages:
        if not check_package(package_name, import_name):
            all_installed = False
    
    print("=" * 40)
    if all_installed:
        print("🎉 所有依赖包都已正确安装！")
        print("现在可以运行 main.py 或 main_improved.py")
    else:
        print("❌ 有依赖包未安装，请运行:")
        print("pip install -r requirements.txt")
        sys.exit(1)

if __name__ == "__main__":
    main()
